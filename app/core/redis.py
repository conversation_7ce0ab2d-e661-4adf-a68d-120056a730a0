"""Redis Service for API Key Management"""

import json
import hashlib
import ssl
import os
from typing import Optional, Dict, Any
from datetime import datetime, timezone

import redis
from redis.exceptions import ConnectionError, TimeoutError, RedisError

from app.core.config import settings
from loguru import logger


class RedisService:
    
    def __init__(self):
        """Initialize Redis service with connection."""
        self._client: Optional[redis.Redis] = None
        self._connect()
    
    def _connect(self) -> None:
        """
        Establish connection to Redis.
        
        Supports both URL-based connection (production) and
        fallback to localhost (development).
        """
        try:
            if settings.REDIS_URL:
                # Try TLS connection first
                if settings.REDIS_URL.startswith('rediss://'):
                    try:
                        logger.info("Attempting TLS connection to Redis...")

                        # Check for CA certificate file
                        ca_cert_path = None
                        possible_paths = [
                            '/app/redis_ca.pem',  # Render deployment
                            './redis_ca.pem',     # Local development
                            'redis_ca.pem'        # Current directory
                        ]

                        for path in possible_paths:
                            if os.path.exists(path):
                                ca_cert_path = path
                                logger.info(f"Found Redis CA certificate at: {path}")
                                break

                        # Configure TLS with or without CA certificate
                        tls_kwargs = {
                            'decode_responses': True,
                            'socket_connect_timeout': 10,
                            'socket_timeout': 10,
                            'retry_on_timeout': True,
                            'health_check_interval': 30
                        }

                        if ca_cert_path:
                            # Use proper TLS with CA certificate
                            tls_kwargs.update({
                                'ssl_ca_certs': ca_cert_path,
                                'ssl_cert_reqs': ssl.CERT_REQUIRED,
                                'ssl_check_hostname': True
                            })
                            logger.info("Using TLS with CA certificate verification")
                        else:
                            # Fallback to insecure TLS
                            tls_kwargs.update({
                                'ssl_cert_reqs': ssl.CERT_NONE,
                                'ssl_check_hostname': False,
                                'ssl_ca_certs': None
                            })
                            logger.warning("Using TLS without certificate verification")

                        self._client = redis.from_url(settings.REDIS_URL, **tls_kwargs)

                        # Test TLS connection
                        self._client.ping()
                        logger.info("TLS connection to Redis successful")

                    except Exception as tls_error:
                        logger.warning(f"TLS connection failed: {tls_error}")
                        logger.info("Attempting non-TLS connection as fallback...")
                        # Fallback to non-TLS
                        non_tls_url = settings.REDIS_URL.replace('rediss://', 'redis://')
                        self._client = redis.from_url(
                            non_tls_url,
                            decode_responses=True,
                            socket_connect_timeout=10,
                            socket_timeout=10,
                            retry_on_timeout=True,
                            health_check_interval=30
                        )
                else:
                    # Non-TLS connection
                    self._client = redis.from_url(
                        settings.REDIS_URL,
                        decode_responses=True,
                        socket_connect_timeout=10,
                        socket_timeout=10,
                        retry_on_timeout=True,
                        health_check_interval=30
                    )
            else:
                # Fallback for development
                self._client = redis.Redis(
                    host='localhost',
                    port=6379,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True
                )
            
            # Test connection
            self._client.ping()
            logger.info("Redis connection established successfully")
            
        except (ConnectionError, TimeoutError) as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self._client = None
        except Exception as e:
            logger.error(f"Unexpected error connecting to Redis: {e}")
            self._client = None
    
    def _ensure_connection(self) -> bool:
        """
        Ensure Redis connection is active.
        
        Returns:
            bool: True if connection is active, False otherwise
        """
        if not self._client:
            self._connect()
        
        if self._client:
            try:
                self._client.ping()
                return True
            except (ConnectionError, TimeoutError):
                logger.warning("Redis connection lost, attempting to reconnect...")
                self._connect()
                return self._client is not None
        
        return False
    
    def _hash_api_key(self, api_key: str) -> str:
        """
        Generate SHA-256 hash of API key.
        
        Args:
            api_key: The raw API key string
            
        Returns:
            str: SHA-256 hash of the API key
        """
        return hashlib.sha256(api_key.encode()).hexdigest()
    
    def store_api_key(self, api_key_hash: str, tenant_id: str, created_at: Optional[datetime] = None) -> bool:
        """
        Store API key hash with tenant information in Redis.
        
        Args:
            api_key_hash: SHA-256 hash of the API key
            tenant_id: UUID of the tenant
            created_at: When the key was created (defaults to now)
            
        Returns:
            bool: True if stored successfully, False otherwise
        """
        if not self._ensure_connection():
            logger.error("Cannot store API key: Redis connection unavailable")
            return False
        
        try:
            key = f"api_key:{api_key_hash}"
            value = {
                "tenant_id": tenant_id,
                "created_at": (created_at or datetime.now(timezone.utc)).isoformat(),
                "revoked": False
            }
            
            result = self._client.set(key, json.dumps(value))
            
            if result:
                logger.debug(f"API key hash stored for tenant {tenant_id}")
                return True
            else:
                logger.error(f"Failed to store API key hash for tenant {tenant_id}")
                return False
                
        except RedisError as e:
            logger.error(f"Redis error storing API key: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error storing API key: {e}")
            return False
    
    def get_api_key_info(self, api_key: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve API key information by raw API key.
        
        Args:
            api_key: The raw API key string
            
        Returns:
            Dict with tenant_id, created_at, revoked status, or None if not found
        """
        if not self._ensure_connection():
            logger.error("Cannot retrieve API key: Redis connection unavailable")
            return None
        
        try:
            api_key_hash = self._hash_api_key(api_key)
            key = f"api_key:{api_key_hash}"
            
            value = self._client.get(key)
            
            if value:
                data = json.loads(value)
                logger.debug(f"API key found for tenant {data.get('tenant_id')}")
                return data
            else:
                logger.debug("API key not found in Redis cache")
                return None
                
        except (json.JSONDecodeError, RedisError) as e:
            logger.error(f"Error retrieving API key: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error retrieving API key: {e}")
            return None
    
    def revoke_api_key(self, api_key_hash: str) -> bool:
        """
        Mark an API key as revoked.
        
        Args:
            api_key_hash: SHA-256 hash of the API key
            
        Returns:
            bool: True if revoked successfully, False otherwise
        """
        if not self._ensure_connection():
            logger.error("Cannot revoke API key: Redis connection unavailable")
            return False
        
        try:
            key = f"api_key:{api_key_hash}"
            value = self._client.get(key)
            
            if value:
                data = json.loads(value)
                data["revoked"] = True
                
                result = self._client.set(key, json.dumps(data))
                
                if result:
                    logger.info(f"API key revoked for tenant {data.get('tenant_id')}")
                    return True
                else:
                    logger.error("Failed to update API key revocation status")
                    return False
            else:
                logger.warning("Attempted to revoke non-existent API key")
                return False
                
        except (json.JSONDecodeError, RedisError) as e:
            logger.error(f"Error revoking API key: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error revoking API key: {e}")
            return False
    
    def delete_api_key(self, api_key_hash: str) -> bool:
        """
        Delete an API key from Redis.
        
        Args:
            api_key_hash: SHA-256 hash of the API key
            
        Returns:
            bool: True if deleted successfully, False otherwise
        """
        if not self._ensure_connection():
            logger.error("Cannot delete API key: Redis connection unavailable")
            return False
        
        try:
            key = f"api_key:{api_key_hash}"
            result = self._client.delete(key)
            
            if result:
                logger.info("API key deleted from Redis")
                return True
            else:
                logger.warning("Attempted to delete non-existent API key")
                return False
                
        except RedisError as e:
            logger.error(f"Error deleting API key: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error deleting API key: {e}")
            return False
    
    def health_check(self) -> bool:
        """
        Check if Redis is healthy and responsive.
        
        Returns:
            bool: True if Redis is healthy, False otherwise
        """
        try:
            if self._ensure_connection():
                self._client.ping()
                return True
            return False
        except Exception:
            return False


# Global Redis service instance
redis_service = RedisService()


def get_redis_service() -> RedisService:
    """
    Get the global Redis service instance.
    
    Returns:
        RedisService: The global Redis service instance
    """
    return redis_service
