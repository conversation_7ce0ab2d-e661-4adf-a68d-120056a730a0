from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
from sqlalchemy import text

from app.api.v1.endpoints import auth, admin
from app.db.base import SessionLocal
from app.middleware.security import SecurityHeadersMiddleware, TimingAttackProtectionMiddleware
from app.middleware.simple_rate_limit import SimpleRateLimitMiddleware
from app.middleware.api_key import APIKeyMiddleware
from app.core.logging import setup_logging, get_security_logger
from app.core.config import settings

setup_logging()
security_logger = get_security_logger()

app = FastAPI(
    title="Authentication Microservice",
    description="Secure multi-tenant authentication service with JWT tokens and API key protection",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(TimingAttackProtectionMiddleware, min_response_time=0.1)
app.add_middleware(SimpleRateLimitMiddleware)

app.add_middleware(APIKeyMiddleware)

origins = settings.get_cors_origins() if hasattr(settings, 'get_cors_origins') else ["*"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(auth.router, prefix="/api/v1/auth", tags=["auth"])
app.include_router(admin.router, prefix="/api/v1/admin", tags=["admin"], include_in_schema=False)



def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    from fastapi.openapi.utils import get_openapi

    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    openapi_schema["components"]["securitySchemes"] = {
        "APIKeyHeader": {
            "type": "apiKey",
            "in": "header",
            "name": "X-API-Key",
            "description": "API Key for tenant authentication"
        }
    }

    for path, path_item in openapi_schema["paths"].items():
        if path in ["/health", "/docs", "/redoc", "/openapi.json"]:
            continue

        # Add security requirement to all methods in protected paths
        for method, operation in path_item.items():
            if method.lower() in ["get", "post", "put", "patch", "delete"]:
                operation["security"] = [{"APIKeyHeader": []}]

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi

start_time = datetime.now()

@app.get("/health")
def health_check():
    uptime = datetime.now() - start_time
    db_status = "ok"
    db = None # Initialize db to None
    try:
        db = SessionLocal()
        db.execute(text("SELECT 1"))
    except Exception as e:
        db_status = f"error: {e}"
    finally:
        if db:
            db.close()

    return {
        "status": "ok",
        "uptime": str(uptime),
        "database_status": db_status
    }
