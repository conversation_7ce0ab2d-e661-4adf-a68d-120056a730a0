"""
Dependencies for API Endpoints

This module provides common dependencies used across API endpoints,
such as database session management and current user retrieval.

Functions:
    get_db: Dependency that provides a SQLAlchemy session.
    get_current_user: Dependency to retrieve the currently authenticated user,
                      with tenant isolation.
    get_tenant_user_by_email: Helper to fetch a user by email within a specific tenant.
    get_tenant_user_by_username: Helper to fetch a user by username within a specific tenant.

Key Features:
    - Manages database sessions for FastAPI routes.
    - Handles JWT token validation and user authentication.
    - Ensures tenant isolation for user data access.
"""
from typing import Generator
import uuid

from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordBearer
from jose import jwt
from pydantic import ValidationError
from sqlalchemy.orm import Session

from app.core import security
from app.core.config import settings
from app.db.base import SessionLocal
from app.db.models import User
from app.api.v1 import schemas
from app.middleware.api_key import get_current_tenant_id

reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"/api/v1/auth/login"
)

def get_db() -> Generator:
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()

def get_current_user(
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(reusable_oauth2)
) -> User:
    """
    Get current authenticated user with tenant isolation.

    Validates JWT token and ensures user belongs to the current tenant.
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        user_id_str = payload.get("sub")
        tenant_id_from_token = payload.get("tenant_id")

        if user_id_str is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Could not validate credentials",
            )

        try:
            user_id = uuid.UUID(user_id_str)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid user ID format",
            )

    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )

    # Get tenant_id from the request context (from middleware)
    current_tenant_id = get_current_tenant_id(request)

    # Check if the token's tenant matches the API-Key's tenant
    if tenant_id_from_token and tenant_id_from_token != current_tenant_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Token tenant mismatch",
        )

    # Fetch user with tenant isolation
    user = db.query(User).filter(
        User.id == user_id,
        User.tenant_id == current_tenant_id
    ).first()

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if not user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")

    return user


def get_tenant_user_by_email(
    email: str,
    tenant_id: str,
    db: Session
) -> User:
    """
    Helper function to get user by email within a specific tenant.
    """
    return db.query(User).filter(
        User.email == email,
        User.tenant_id == tenant_id
    ).first()



