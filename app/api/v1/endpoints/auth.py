from datetime import timed<PERSON><PERSON>, datetime, timezone
import uuid
from urllib.parse import urlparse

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Request, status
from fastapi.security import OAuth2Pass<PERSON>RequestForm
from sqlalchemy.orm import Session

from app.api.v1 import deps, schemas
from app.core import security
from app.core.config import settings
from app.core.email import send_password_reset_email
from app.core.logging import get_security_logger # Changed import
from app.db.models import User, Tenant # Importar Tenant
from app.middleware.api_key import get_current_tenant_id, get_tenant_context

router = APIRouter()

security_logger = get_security_logger() # Added instantiation


@router.post("/register", response_model=schemas.User)
def register(
    request: Request,
    user_in: schemas.UserCreate,
    db: Session = Depends(deps.get_db),
    tenant_context: dict = Depends(get_tenant_context)
):
    """
    Register a new user within the current tenant.

    Users are isolated by tenant - same email can exist in different tenants.
    This endpoint requires an API key to identify the tenant.
    """
    tenant_id = tenant_context["tenant_id"]

    existing_user = deps.get_tenant_user_by_email(
        user_in.email, tenant_id, db
    )
    if existing_user:
        raise HTTPException(
            status_code=400,
            detail=f"Email '{user_in.email}' already exists in this application.",
        )

    hashed_password = security.get_password_hash(user_in.password)
    db_user = User(
        tenant_id=tenant_id,
        email=user_in.email,
        hashed_password=hashed_password,
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    security_logger.info(
        "User registered successfully",
        extra={
            "component": "auth_register",
            "user_id": str(db_user.id),
            "email": db_user.email,
            "tenant_id": tenant_id,
            "tenant_name": tenant_context["tenant_name"]
        }
    )

    return db_user


@router.post("/login", response_model=schemas.Token)
def login(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(deps.get_db),
    tenant_context: dict = Depends(get_tenant_context)
):
    """
    OAuth2 compatible token login with tenant isolation.

    Note: Use email in the 'username' field of the form.
    Users are authenticated within their tenant context only.
    This endpoint requires an API key to identify the tenant.
    """
    tenant_id = tenant_context["tenant_id"]

    email = form_data.username

    user = deps.get_tenant_user_by_email(email, tenant_id, db)

    if not user or not security.verify_password(
        form_data.password, user.hashed_password
    ):
        security_logger.log_login_attempt(
            request=request,
            email=email,
            success=False,
            failure_reason="invalid_credentials"
        )
        raise HTTPException(
            status_code=400, detail="Incorrect email or password"
        )

    if not user.is_active:
        security_logger.log_login_attempt(
            request=request,
            email=email,
            success=False,
            user_id=user.id,
            failure_reason="inactive_user"
        )
        raise HTTPException(status_code=400, detail="Inactive user")

    security_logger.info(
        "User login successful",
        extra={
            "component": "auth_login",
            "user_id": str(user.id),
            "email": email,
            "tenant_id": tenant_id,
            "tenant_name": tenant_context["tenant_name"],
            "client_ip": request.client.host if request.client else "unknown"
        }
    )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    token_data = {
        "sub": str(user.id),
        "email": user.email,
        "tenant_id": tenant_id
    }

    access_token = security.create_access_token(
        data=token_data,
        expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "tenant_id": tenant_id,
        "tenant_name": tenant_context["tenant_name"]
    }


@router.get("/me", response_model=schemas.User)
def read_users_me(current_user: User = Depends(deps.get_current_user)):
    """
    Fetch the current logged in user.
    """
    return current_user


@router.post("/forgot-password", response_model=schemas.Message)
async def forgot_password(
    background_tasks: BackgroundTasks,
    request_data: schemas.PasswordResetRequest,
    request: Request,
    db: Session = Depends(deps.get_db),
    tenant_context: dict = Depends(get_tenant_context)
):
    """
    Requests a password reset by sending an email with a token.
    The frontend URL for the reset link is dynamically determined from the 'Origin' header
    and validated against the tenant's allowed origins.
    """
    # Obter tenant_id do Origin da requisicao
    origin_url = request.headers.get("Origin")
    if not origin_url:
        security_logger.warning(
            "Forgot password request denied: Missing Origin header",
            extra={"component": "auth_forgot_password", "email": request_data.email, "client_ip": request.client.host if request.client else "unknown"}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Origin header is required for password reset requests."
        )

    # Basic validation of the Origin header format
    parsed_origin = urlparse(origin_url)
    if not parsed_origin.scheme or not parsed_origin.netloc:
        security_logger.warning(
            "Forgot password request denied: Invalid Origin header format",
            extra={"component": "auth_forgot_password", "email": request_data.email, "origin": origin_url, "client_ip": request.client.host if request.client else "unknown"}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid Origin header format."
        )

    # Buscar o tenant pelo allowed_frontend_origins
    tenant = db.query(Tenant).filter(Tenant.allowed_frontend_origins.contains([origin_url])).first()
    if not tenant:
        security_logger.warning(
            "Forgot password request denied: Untrusted Origin for tenant",
            extra={"component": "auth_forgot_password", "email": request_data.email, "origin": origin_url, "client_ip": request.client.host if request.client else "unknown", "allowed_origins": tenant.allowed_frontend_origins if tenant else "N/A"}
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Untrusted origin for password reset request."
        )
    
    tenant_id = tenant.id # Definir tenant_id a partir do tenant encontrado

    user = db.query(User).filter(User.email == request_data.email).first()

    if not user:
        return {"message": "If the email is registered, you will receive instructions to reset your password."}

    if not user.is_active:
        return {"message": "If the email is registered, you will receive instructions to reset your password."}

    reset_token = security.create_password_reset_token(user.email)

    user.reset_token = reset_token
    user.reset_token_expires = datetime.now() + timedelta(hours=24)
    db.add(user)
    db.commit()

    background_tasks.add_task(
        send_password_reset_email,
        email_to=user.email,
        token=reset_token,
        frontend_url=origin_url
    )

    return {"message": "If the email is registered, you will receive instructions to reset your password."}


@router.post("/reset-password", response_model=schemas.Message)
def reset_password(
    request: schemas.PasswordResetConfirm,
    db: Session = Depends(deps.get_db),
    tenant_context: dict = Depends(get_tenant_context)
):
    """
    Resets the password using the token received by email.
    """
    # Verify the token
    email = security.verify_password_reset_token(request.token)
    if not email:
        raise HTTPException(
            status_code=400,
            detail="Invalid or expired token"
        )

    # Find the user by email
    user = db.query(User).filter(User.email == email).first()
    if not user:
        raise HTTPException(
            status_code=404,
            detail="User not found"
        )

    # Check if the token has expired
    if not user.reset_token_expires or user.reset_token_expires < datetime.now(timezone.utc):
        raise HTTPException(
            status_code=400,
            detail="Expired token"
        )

    # Check if the token matches the stored one
    if user.reset_token != request.token:
        raise HTTPException(
            status_code=400,
            detail="Invalid token"
        )

    # Update the password
    user.hashed_password = security.get_password_hash(request.new_password)

    # Clear the reset fields
    user.reset_token = None
    user.reset_token_expires = None

    db.add(user)
    db.commit()

    return {"message": "Password updated successfully"}


@router.post("/change-password", response_model=schemas.Message)
def change_password(
    request: schemas.PasswordChange,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    Changes the password of a logged-in user.
    Requires authentication and validation of the current password.
    """
    # Check if the current password is correct
    if not security.verify_password(request.old_password, current_user.hashed_password):
        raise HTTPException(
            status_code=400,
            detail="Incorrect current password"
        )

    # Check if the new password is different from the current one
    if security.verify_password(request.new_password, current_user.hashed_password):
        raise HTTPException(
            status_code=400,
            detail="The new password must be different from the current password"
        )

    # Update the password
    current_user.hashed_password = security.get_password_hash(request.new_password)

    # Clear any existing reset token (for security)
    current_user.reset_token = None
    current_user.reset_token_expires = None

    db.add(current_user)
    db.commit()

    return {"message": "Password changed successfully"}
