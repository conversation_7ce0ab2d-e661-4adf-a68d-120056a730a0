"""
Database Initialization and Session Management

This module sets up the SQLAlchemy engine and session factory for the application.
It provides the base for declarative models and a utility for managing database sessions.

Variables:
    engine: The SQLAlchemy engine connected to the database.
    SessionLocal: A sessionmaker configured for local database sessions.
    Base: The declarative base class for SQLAlchemy models.

Key Features:
    - Centralized database connection setup.
    - Manages database sessions for consistent data access.
    - Provides the foundation for defining ORM models.
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base

from app.core.config import settings

engine = create_engine(settings.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()
