import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from sqlalchemy.orm import Session
from unittest.mock import patch

from app.db.models import User, Tenant


def test_forgot_password_success(client: TestClient, db: Session, test_user, test_tenant):
    """Test successful forgot password request."""
    # Update tenant to allow the origin
    test_tenant.allowed_frontend_origins = ["http://localhost:3000"]
    db.commit()

    response = client.post(
        "/api/v1/auth/forgot-password",
        json={"email": test_user.email},
        headers={
            "Origin": "http://localhost:3000",
            "X-API-Key": test_tenant._test_api_key
        }
    )

    assert response.status_code == 200
    assert "If the email is registered" in response.json()["message"]

    # Check that reset token was set (email is sent in background)
    db.refresh(test_user)
    assert test_user.reset_token is not None
    assert test_user.reset_token_expires is not None


def test_forgot_password_nonexistent_user(client: TestClient, test_tenant, db: Session):
    """Test forgot password with nonexistent user returns same message."""
    # Update tenant to allow the origin
    test_tenant.allowed_frontend_origins = ["http://localhost:3000"]
    db.commit()

    response = client.post(
        "/api/v1/auth/forgot-password",
        json={"email": "<EMAIL>"},
        headers={
            "Origin": "http://localhost:3000",
            "X-API-Key": test_tenant._test_api_key
        }
    )
    
    assert response.status_code == 200
    assert "If the email is registered" in response.json()["message"]


def test_forgot_password_inactive_user(client: TestClient, db: Session, test_tenant):
    """Test forgot password with inactive user returns same message."""
    # Create inactive user
    from app.core.security import get_password_hash
    inactive_user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password123"),
        tenant_id=test_tenant.id,
        is_active=False
    )
    db.add(inactive_user)
    
    # Update tenant to allow the origin
    test_tenant.allowed_frontend_origins = ["http://localhost:3000"]
    db.commit()
    
    response = client.post(
        "/api/v1/auth/forgot-password",
        json={"email": inactive_user.email},
        headers={
            "Origin": "http://localhost:3000",
            "X-API-Key": test_tenant._test_api_key
        }
    )
    
    assert response.status_code == 200
    assert "If the email is registered" in response.json()["message"]


def test_forgot_password_missing_origin(client: TestClient, test_user, test_tenant):
    """Test forgot password fails without Origin header."""
    response = client.post(
        "/api/v1/auth/forgot-password",
        json={"email": test_user.email},
        headers={"X-API-Key": test_tenant._test_api_key}
    )

    assert response.status_code == 400
    assert "Origin header is required" in response.json()["detail"]


def test_forgot_password_invalid_origin_format(client: TestClient, test_user, test_tenant):
    """Test forgot password fails with invalid Origin format."""
    response = client.post(
        "/api/v1/auth/forgot-password",
        json={"email": test_user.email},
        headers={
            "Origin": "invalid-url",
            "X-API-Key": test_tenant._test_api_key
        }
    )

    assert response.status_code == 400
    assert "Invalid Origin header format" in response.json()["detail"]


def test_forgot_password_untrusted_origin(client: TestClient, test_user, test_tenant, db: Session):
    """Test forgot password fails with untrusted origin."""
    # Set allowed origins that don't include the test origin
    test_tenant.allowed_frontend_origins = ["http://allowed.com"]
    db.commit()
    
    response = client.post(
        "/api/v1/auth/forgot-password",
        json={"email": test_user.email},
        headers={
            "Origin": "http://untrusted.com",
            "X-API-Key": test_tenant._test_api_key
        }
    )
    
    assert response.status_code == 403
    assert "Untrusted origin" in response.json()["detail"]
