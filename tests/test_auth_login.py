import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session
from uuid import UUID

from app.db.models import User


def test_login_success(client: TestClient, db: Session, tenant_headers, test_user):
    """Test successful user login with valid credentials."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "password123"},
        headers=tenant_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Check response data
    assert data["token_type"] == "bearer"
    assert "access_token" in data
    assert data["tenant_id"] == str(test_user.tenant_id)
    assert data["tenant_name"] == "Test Tenant"


def test_login_invalid_credentials(client: TestClient, tenant_headers, test_user):
    """Test login fails with invalid password."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "wrongpassword"},
        headers=tenant_headers
    )
    
    assert response.status_code == 400
    assert "Incorrect email or password" in response.json()["detail"]


def test_login_nonexistent_user(client: TestClient, tenant_headers):
    """Test login fails with nonexistent user."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "<EMAIL>", "password": "password123"},
        headers=tenant_headers
    )
    
    assert response.status_code == 400
    assert "Incorrect email or password" in response.json()["detail"]


def test_login_inactive_user(client: TestClient, db: Session, tenant_headers, test_tenant):
    """Test login fails with inactive user."""
    # Create inactive user
    from app.core.security import get_password_hash
    inactive_user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password123"),
        tenant_id=test_tenant.id,
        is_active=False
    )
    db.add(inactive_user)
    db.commit()
    
    response = client.post(
        "/api/v1/auth/login",
        data={"username": inactive_user.email, "password": "password123"},
        headers=tenant_headers
    )
    
    assert response.status_code == 400
    assert "Inactive user" in response.json()["detail"]


def test_login_without_api_key(client: TestClient, test_user):
    """Test login fails without API key."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "password123"}
    )

    assert response.status_code == 401
    assert "API key required" in response.json()["detail"]


def test_login_with_invalid_api_key(client: TestClient, test_user):
    """Test login fails with invalid API key."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "password123"},
        headers={"X-API-Key": "invalid_key"}
    )

    assert response.status_code == 401
    assert "Invalid API key" in response.json()["detail"]
