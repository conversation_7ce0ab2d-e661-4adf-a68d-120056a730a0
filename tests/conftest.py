import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
import uuid

from app.main import app
from app.db.models import Base, User, Tenant
from app.core.security import get_password_hash
from app.api.v1 import deps
from app.core import security

# Override the dependency to use test database
# Use PostgreSQL for tests to match production environment and support ARRAY types
TEST_DATABASE_URL = "postgresql+psycopg://quanthea_admin:f496c6027f65676e8cb9d1350fc6fe52e21f330f33fcbc444e05e5dacad1b775@db:5432/auth_db"

engine = create_engine(TEST_DATABASE_URL)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="function")
def db():
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)


# Override the get_db dependency
def override_get_db():
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


app.dependency_overrides[deps.get_db] = override_get_db


@pytest.fixture(scope="function")
def client():
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="function")
def test_tenant(db):
    # Create a real API key and hash it
    test_api_key = "test_api_key_12345678901234567890"
    api_key_hash = security.get_password_hash(test_api_key)

    tenant = Tenant(
        name="Test Tenant",
        description="Test tenant for unit tests",
        is_active=True,
        api_key_hash=api_key_hash,
        allowed_frontend_origins=["http://localhost:3000", "http://localhost:8080"]
    )
    db.add(tenant)
    db.commit()
    db.refresh(tenant)

    # Store the plain API key for use in tests
    tenant._test_api_key = test_api_key
    return tenant


@pytest.fixture(scope="function")
def tenant_headers(test_tenant):
    # Use the real API key that matches the hashed version in the database
    return {"X-API-Key": test_tenant._test_api_key}


@pytest.fixture(scope="function")
def test_user(db, test_tenant):
    """Create a test user for authentication tests."""
    from app.core.security import get_password_hash
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password123"),
        tenant_id=test_tenant.id,
        is_active=True
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


@pytest.fixture(scope="function")
def user_token_headers(test_user, test_tenant):
    """Create authentication headers with JWT token AND API key for test user."""
    from app.core.security import create_access_token
    from datetime import timedelta

    access_token_expires = timedelta(minutes=30)
    token_data = {
        "sub": str(test_user.id),
        "email": test_user.email,
        "tenant_id": str(test_user.tenant_id)
    }

    access_token = create_access_token(
        data=token_data,
        expires_delta=access_token_expires
    )

    return {
        "Authorization": f"Bearer {access_token}",
        "X-API-Key": test_tenant._test_api_key
    }


@pytest.fixture(scope="function")
def test_admin_user(db):
    """Create a test admin user for admin tests."""
    from app.core.security import get_password_hash
    from app.db.models import AdminUser

    admin_user = AdminUser(
        email="<EMAIL>",
        hashed_password=get_password_hash("admin123"),
        is_active=True
    )
    db.add(admin_user)
    db.commit()
    db.refresh(admin_user)
    return admin_user


@pytest.fixture(scope="function")
def admin_token_headers(test_admin_user):
    """Create authentication headers with JWT token for admin user."""
    from app.core.security import create_access_token
    from datetime import timedelta

    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        subject=test_admin_user.email,
        expires_delta=access_token_expires
    )

    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture(scope="function")
def test_user(db, test_tenant):
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password123"),
        tenant_id=test_tenant.id,
        is_active=True
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user