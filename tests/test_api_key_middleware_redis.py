"""Tests for API Key Middleware with Redis integration"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi import Fast<PERSON><PERSON>
from fastapi.testclient import TestClient

from app.middleware.api_key import APIKeyMiddleware
from app.db.models import Tenant


@pytest.fixture
def app():
    """FastAPI app with middleware"""
    app = FastAPI()
    app.add_middleware(APIKeyMiddleware)
    
    @app.get("/test")
    async def test_endpoint():
        return {"message": "success"}
    
    @app.get("/health")
    async def health_endpoint():
        return {"status": "ok"}
    
    return app


@pytest.fixture
def client(app):
    """Test client"""
    return TestClient(app)


@pytest.fixture
def mock_tenant():
    """Mock tenant object"""
    tenant = Mock(spec=Tenant)
    tenant.id = "test-tenant-id"
    tenant.name = "Test Tenant"
    tenant.is_active = True
    tenant.api_key_hash = "hashed_api_key"
    return tenant


class TestAPIKeyMiddlewareRedis:
    
    def test_bypass_paths(self, client):
        """Test that bypass paths work without API key"""
        response = client.get("/health")
        assert response.status_code == 200

        response = client.get("/docs")
        assert response.status_code == 200  # FastAPI auto-generates docs
    
    def test_missing_api_key(self, client):
        """Test request without API key"""
        response = client.get("/test")
        assert response.status_code == 401
        assert response.json()["error_code"] == "MISSING_API_KEY"
    
    @patch('app.middleware.api_key.get_redis_service')
    def test_redis_cache_hit(self, mock_get_redis, client, mock_tenant):
        """Test successful authentication via Redis cache"""
        # Mock Redis service
        mock_redis = Mock()
        mock_redis.get_api_key_info.return_value = {
            "tenant_id": "test-tenant-id",
            "created_at": "2025-01-01T00:00:00Z",
            "revoked": False
        }
        mock_get_redis.return_value = mock_redis
        
        # Mock database query
        with patch('app.middleware.api_key.SessionLocal') as mock_session:
            mock_db = Mock()
            mock_session.return_value = mock_db
            mock_db.query.return_value.filter.return_value.first.return_value = mock_tenant
            
            response = client.get("/test", headers={"X-API-Key": "valid-api-key"})
            
            assert response.status_code == 200
            assert response.json() == {"message": "success"}
            
            # Verify Redis was called
            mock_redis.get_api_key_info.assert_called_once_with("valid-api-key")
            
            # Verify database was called for tenant details
            mock_db.query.assert_called_once()
    
    @patch('app.middleware.api_key.get_redis_service')
    def test_redis_cache_miss_db_hit(self, mock_get_redis, client, mock_tenant):
        """Test authentication via database fallback when Redis cache misses"""
        # Mock Redis service - cache miss
        mock_redis = Mock()
        mock_redis.get_api_key_info.return_value = None
        mock_redis._hash_api_key.return_value = "hashed_key"
        mock_redis.store_api_key.return_value = True
        mock_get_redis.return_value = mock_redis
        
        # Mock database query and password verification
        with patch('app.middleware.api_key.SessionLocal') as mock_session, \
             patch('app.middleware.api_key.security.verify_password') as mock_verify:
            
            mock_db = Mock()
            mock_session.return_value = mock_db
            mock_db.query.return_value.filter.return_value.all.return_value = [mock_tenant]
            mock_verify.return_value = True
            
            response = client.get("/test", headers={"X-API-Key": "valid-api-key"})
            
            assert response.status_code == 200
            
            # Verify Redis cache miss
            mock_redis.get_api_key_info.assert_called_once_with("valid-api-key")
            
            # Verify database fallback
            mock_verify.assert_called_once_with("valid-api-key", "hashed_api_key")
            
            # Verify key was stored in Redis for future requests
            mock_redis.store_api_key.assert_called_once()
    
    @patch('app.middleware.api_key.get_redis_service')
    def test_revoked_key_in_redis(self, mock_get_redis, client):
        """Test revoked key in Redis cache"""
        # Mock Redis service - revoked key
        mock_redis = Mock()
        mock_redis.get_api_key_info.return_value = {
            "tenant_id": "test-tenant-id",
            "created_at": "2025-01-01T00:00:00Z",
            "revoked": True
        }
        mock_get_redis.return_value = mock_redis
        
        # Mock database query - no results for revoked key
        with patch('app.middleware.api_key.SessionLocal') as mock_session, \
             patch('app.middleware.api_key.security.verify_password') as mock_verify:
            
            mock_db = Mock()
            mock_session.return_value = mock_db
            mock_db.query.return_value.filter.return_value.all.return_value = []
            mock_verify.return_value = False
            
            response = client.get("/test", headers={"X-API-Key": "revoked-api-key"})
            
            assert response.status_code == 401
            assert response.json()["error_code"] == "INVALID_API_KEY"
    
    @patch('app.middleware.api_key.get_redis_service')
    def test_inactive_tenant(self, mock_get_redis, client):
        """Test inactive tenant in Redis cache"""
        # Mock Redis service
        mock_redis = Mock()
        mock_redis.get_api_key_info.return_value = {
            "tenant_id": "inactive-tenant-id",
            "created_at": "2025-01-01T00:00:00Z",
            "revoked": False
        }
        mock_redis.delete_api_key.return_value = True
        mock_redis._hash_api_key.return_value = "hashed_key"
        mock_get_redis.return_value = mock_redis
        
        # Mock inactive tenant
        inactive_tenant = Mock(spec=Tenant)
        inactive_tenant.is_active = False
        
        with patch('app.middleware.api_key.SessionLocal') as mock_session:
            mock_db = Mock()
            mock_session.return_value = mock_db
            mock_db.query.return_value.filter.return_value.first.return_value = inactive_tenant
            mock_db.query.return_value.filter.return_value.all.return_value = []  # Empty list for fallback

            response = client.get("/test", headers={"X-API-Key": "inactive-tenant-key"})
            
            assert response.status_code == 401
            
            # Verify key was removed from Redis
            mock_redis.delete_api_key.assert_called_once()
    
    @patch('app.middleware.api_key.get_redis_service')
    def test_redis_unavailable_fallback(self, mock_get_redis, client, mock_tenant):
        """Test fallback to database when Redis is unavailable"""
        # Mock Redis service - unavailable
        mock_redis = Mock()
        mock_redis.get_api_key_info.return_value = None
        mock_get_redis.return_value = mock_redis
        
        # Mock database query
        with patch('app.middleware.api_key.SessionLocal') as mock_session, \
             patch('app.middleware.api_key.security.verify_password') as mock_verify:
            
            mock_db = Mock()
            mock_session.return_value = mock_db
            mock_db.query.return_value.filter.return_value.all.return_value = [mock_tenant]
            mock_verify.return_value = True
            
            response = client.get("/test", headers={"X-API-Key": "valid-api-key"})
            
            assert response.status_code == 200
            
            # Verify database was used
            mock_verify.assert_called_once_with("valid-api-key", "hashed_api_key")
    
    @patch('app.middleware.api_key.get_redis_service')
    def test_invalid_api_key(self, mock_get_redis, client):
        """Test completely invalid API key"""
        # Mock Redis service - not found
        mock_redis = Mock()
        mock_redis.get_api_key_info.return_value = None
        mock_get_redis.return_value = mock_redis
        
        # Mock database query - no matching tenants
        with patch('app.middleware.api_key.SessionLocal') as mock_session, \
             patch('app.middleware.api_key.security.verify_password') as mock_verify:
            
            mock_db = Mock()
            mock_session.return_value = mock_db
            mock_db.query.return_value.filter.return_value.all.return_value = []
            mock_verify.return_value = False
            
            response = client.get("/test", headers={"X-API-Key": "invalid-api-key"})
            
            assert response.status_code == 401
            assert response.json()["error_code"] == "INVALID_API_KEY"
    
    @patch('app.middleware.api_key.get_redis_service')
    def test_tenant_context_injection(self, mock_get_redis, client, mock_tenant):
        """Test that tenant context is properly injected"""
        # Mock Redis service
        mock_redis = Mock()
        mock_redis.get_api_key_info.return_value = {
            "tenant_id": "test-tenant-id",
            "created_at": "2025-01-01T00:00:00Z",
            "revoked": False
        }
        mock_get_redis.return_value = mock_redis
        
        with patch('app.middleware.api_key.SessionLocal') as mock_session:
            mock_db = Mock()
            mock_session.return_value = mock_db
            mock_db.query.return_value.filter.return_value.first.return_value = mock_tenant
            
            response = client.get("/test", headers={"X-API-Key": "valid-api-key"})
            
            assert response.status_code == 200
            
            # Check response headers for tenant info
            assert "X-Tenant-Name" in response.headers
            assert "X-Tenant-ID" in response.headers
            assert response.headers["X-Tenant-Name"] == "Test Tenant"
            assert response.headers["X-Tenant-ID"] == "test-tenant-id"
