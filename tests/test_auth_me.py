import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session


def test_me_success(client: TestC<PERSON>, test_user, user_token_headers):
    """Test successful retrieval of current user info."""
    response = client.get(
        "/api/v1/auth/me",
        headers=user_token_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Check response data
    assert data["email"] == test_user.email
    assert data["id"] == str(test_user.id)
    assert data["tenant_id"] == str(test_user.tenant_id)
    assert data["is_active"] == test_user.is_active


def test_me_without_token(client: TestClient):
    """Test /me fails without authentication token."""
    response = client.get("/api/v1/auth/me")

    assert response.status_code == 401
    assert "API key required" in response.json()["detail"]


def test_me_with_invalid_token(client: TestClient, tenant_headers):
    """Test /me fails with invalid token."""
    headers = tenant_headers.copy()
    headers["Authorization"] = "Bearer invalid_token"

    response = client.get(
        "/api/v1/auth/me",
        headers=headers
    )

    assert response.status_code == 403
    assert "Could not validate credentials" in response.json()["detail"]


def test_me_with_expired_token(client: TestClient, tenant_headers):
    """Test /me fails with expired token."""
    # Create an expired token
    from app.core.security import create_access_token
    from datetime import timedelta

    expired_token = create_access_token(
        data={"sub": "<EMAIL>"},
        expires_delta=timedelta(seconds=-1)  # Already expired
    )

    headers = tenant_headers.copy()
    headers["Authorization"] = f"Bearer {expired_token}"

    response = client.get(
        "/api/v1/auth/me",
        headers=headers
    )

    assert response.status_code == 403
    assert "Could not validate credentials" in response.json()["detail"]
