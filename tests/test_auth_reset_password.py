import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session
from datetime import datetime, timezone, timedelta

from app.db.models import User
from app.core.security import create_password_reset_token, verify_password


def test_reset_password_success(client: TestClient, db: Session, test_user, test_tenant):
    """Test successful password reset with valid token."""
    # Create reset token
    reset_token = create_password_reset_token(test_user.email)
    test_user.reset_token = reset_token
    test_user.reset_token_expires = datetime.now(timezone.utc) + timedelta(hours=1)
    db.commit()
    
    new_password = "NewPassword123!"
    
    response = client.post(
        "/api/v1/auth/reset-password",
        json={
            "token": reset_token,
            "new_password": new_password
        },
        headers={"X-API-Key": test_tenant._test_api_key}
    )
    
    assert response.status_code == 200
    assert "Password updated successfully" in response.json()["message"]
    
    # Check that password was updated
    db.refresh(test_user)
    assert verify_password(new_password, test_user.hashed_password)
    
    # Check that reset token was cleared
    assert test_user.reset_token is None
    assert test_user.reset_token_expires is None


def test_reset_password_invalid_token(client: TestClient, test_tenant):
    """Test reset password fails with invalid token."""
    response = client.post(
        "/api/v1/auth/reset-password",
        json={
            "token": "invalid_token",
            "new_password": "NewPassword123!"
        },
        headers={"X-API-Key": test_tenant._test_api_key}
    )
    
    assert response.status_code == 400
    assert "Invalid or expired token" in response.json()["detail"]


def test_reset_password_expired_token(client: TestClient, db: Session, test_user, test_tenant):
    """Test reset password fails with expired token."""
    # Create expired token
    reset_token = create_password_reset_token(test_user.email)
    test_user.reset_token = reset_token
    test_user.reset_token_expires = datetime.now(timezone.utc) - timedelta(hours=1)  # Expired
    db.commit()
    
    response = client.post(
        "/api/v1/auth/reset-password",
        json={
            "token": reset_token,
            "new_password": "NewPassword123!"
        },
        headers={"X-API-Key": test_tenant._test_api_key}
    )
    
    assert response.status_code == 400
    assert "Expired token" in response.json()["detail"]


def test_reset_password_user_not_found(client: TestClient, test_tenant):
    """Test reset password fails when user doesn't exist."""
    # Create token for non-existent user
    reset_token = create_password_reset_token("<EMAIL>")
    
    response = client.post(
        "/api/v1/auth/reset-password",
        json={
            "token": reset_token,
            "new_password": "NewPassword123!"
        },
        headers={"X-API-Key": test_tenant._test_api_key}
    )
    
    assert response.status_code == 404
    assert "User not found" in response.json()["detail"]


def test_reset_password_token_mismatch(client: TestClient, db: Session, test_user, test_tenant):
    """Test reset password fails when token doesn't match stored token."""
    # Set different token in database
    test_user.reset_token = "different_token"
    test_user.reset_token_expires = datetime.now(timezone.utc) + timedelta(hours=1)
    db.commit()
    
    # Try with different token
    reset_token = create_password_reset_token(test_user.email)
    
    response = client.post(
        "/api/v1/auth/reset-password",
        json={
            "token": reset_token,
            "new_password": "NewPassword123!"
        },
        headers={"X-API-Key": test_tenant._test_api_key}
    )
    
    assert response.status_code == 400
    assert "Invalid token" in response.json()["detail"]


def test_reset_password_no_expiry_set(client: TestClient, db: Session, test_user, test_tenant):
    """Test reset password fails when no expiry is set."""
    reset_token = create_password_reset_token(test_user.email)
    test_user.reset_token = reset_token
    test_user.reset_token_expires = None  # No expiry set
    db.commit()
    
    response = client.post(
        "/api/v1/auth/reset-password",
        json={
            "token": reset_token,
            "new_password": "NewPassword123!"
        },
        headers={"X-API-Key": test_tenant._test_api_key}
    )
    
    assert response.status_code == 400
    assert "Expired token" in response.json()["detail"]
