import pytest
from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>
from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session
from unittest.mock import Mock

from app.api.v1 import deps
from app.db.models import User


def test_get_db():
    """Test database session dependency."""
    db_gen = deps.get_db()
    db = next(db_gen)
    
    # Should return a database session
    assert db is not None
    
    # Clean up
    try:
        next(db_gen)
    except StopIteration:
        pass  # Expected behavior


def test_get_current_user_success(db: Session, test_user, test_tenant):
    """Test successful user retrieval from valid token."""
    from app.core.security import create_access_token
    from datetime import timedelta
    
    # Create valid token
    access_token_expires = timedelta(minutes=30)
    token_data = {
        "sub": str(test_user.id),
        "email": test_user.email,
        "tenant_id": str(test_user.tenant_id)
    }
    
    access_token = create_access_token(
        data=token_data,
        expires_delta=access_token_expires
    )
    
    # Mock request with tenant context
    mock_request = Mock()
    mock_request.state.tenant_id = str(test_user.tenant_id)
    
    # Test the function
    user = deps.get_current_user(db=db, token=access_token, request=mock_request)
    
    assert user.id == test_user.id
    assert user.email == test_user.email
    assert user.tenant_id == test_user.tenant_id


def test_get_current_user_invalid_token(db: Session):
    """Test user retrieval fails with invalid token."""
    mock_request = Mock()
    mock_request.state.tenant_id = "some-tenant-id"
    
    with pytest.raises(HTTPException) as exc_info:
        deps.get_current_user(db=db, token="invalid_token", request=mock_request)

    assert exc_info.value.status_code == 403
    assert "Could not validate credentials" in str(exc_info.value.detail)


def test_get_current_user_user_not_found(db: Session, test_tenant):
    """Test user retrieval fails when user doesn't exist."""
    from app.core.security import create_access_token
    from datetime import timedelta

    # Create token for non-existent user
    access_token_expires = timedelta(minutes=30)
    token_data = {
        "sub": "*************-9999-9999-************",  # Non-existent user ID
        "email": "<EMAIL>",
        "tenant_id": str(test_tenant.id)  # Use valid tenant UUID
    }

    access_token = create_access_token(
        data=token_data,
        expires_delta=access_token_expires
    )

    mock_request = Mock()
    mock_request.state.tenant_id = str(test_tenant.id)  # Use valid tenant UUID

    with pytest.raises(HTTPException) as exc_info:
        deps.get_current_user(db=db, token=access_token, request=mock_request)

    assert exc_info.value.status_code == 404
    assert "User not found" in str(exc_info.value.detail)


def test_get_current_user_tenant_mismatch(db: Session, test_user):
    """Test user retrieval fails when tenant doesn't match."""
    from app.core.security import create_access_token
    from datetime import timedelta
    import uuid

    # Create valid token
    access_token_expires = timedelta(minutes=30)
    token_data = {
        "sub": str(test_user.id),
        "email": test_user.email,
        "tenant_id": str(test_user.tenant_id)
    }

    access_token = create_access_token(
        data=token_data,
        expires_delta=access_token_expires
    )

    # Mock request with different tenant (valid UUID)
    mock_request = Mock()
    mock_request.state.tenant_id = str(uuid.uuid4())  # Different but valid UUID

    with pytest.raises(HTTPException) as exc_info:
        deps.get_current_user(db=db, token=access_token, request=mock_request)

    assert exc_info.value.status_code == 403
    assert "Token tenant mismatch" in str(exc_info.value.detail)


def test_get_tenant_user_by_email_success(db: Session, test_user, test_tenant):
    """Test successful user retrieval by email and tenant."""
    user = deps.get_tenant_user_by_email(
        email=test_user.email,
        tenant_id=str(test_user.tenant_id),
        db=db
    )
    
    assert user is not None
    assert user.id == test_user.id
    assert user.email == test_user.email


def test_get_tenant_user_by_email_not_found(db: Session, test_tenant):
    """Test user retrieval returns None when user not found."""
    user = deps.get_tenant_user_by_email(
        email="<EMAIL>",
        tenant_id=str(test_tenant.id),
        db=db
    )
    
    assert user is None


def test_get_tenant_user_by_email_wrong_tenant(db: Session, test_user):
    """Test user retrieval returns None when tenant doesn't match."""
    import uuid

    user = deps.get_tenant_user_by_email(
        email=test_user.email,
        tenant_id=str(uuid.uuid4()),  # Different but valid UUID
        db=db
    )

    assert user is None
