import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session

from app.core.security import verify_password


def test_change_password_success(client: TestClient, db: Session, test_user, user_token_headers):
    """Test successful password change with valid current password."""
    new_password = "NewPassword123!"
    
    response = client.post(
        "/api/v1/auth/change-password",
        json={
            "old_password": "password123",
            "new_password": new_password
        },
        headers=user_token_headers
    )
    
    assert response.status_code == 200
    assert "Password changed successfully" in response.json()["message"]
    
    # Check that password was updated
    db.refresh(test_user)
    assert verify_password(new_password, test_user.hashed_password)
    
    # Check that reset fields were cleared
    assert test_user.reset_token is None
    assert test_user.reset_token_expires is None


def test_change_password_incorrect_current(client: TestClient, user_token_headers):
    """Test change password fails with incorrect current password."""
    response = client.post(
        "/api/v1/auth/change-password",
        json={
            "old_password": "wrongpassword",
            "new_password": "NewPassword123!"
        },
        headers=user_token_headers
    )
    
    assert response.status_code == 400
    assert "Incorrect current password" in response.json()["detail"]


def test_change_password_same_as_current(client: TestClient, user_token_headers):
    """Test change password fails when new password is same as current."""
    response = client.post(
        "/api/v1/auth/change-password",
        json={
            "old_password": "password123",
            "new_password": "password123"  # Same as current
        },
        headers=user_token_headers
    )
    
    assert response.status_code == 400
    assert "The new password must be different" in response.json()["detail"]


def test_change_password_without_auth(client: TestClient):
    """Test change password fails without authentication."""
    response = client.post(
        "/api/v1/auth/change-password",
        json={
            "old_password": "password123",
            "new_password": "NewPassword123!"
        }
    )

    assert response.status_code == 401
    assert "API key required" in response.json()["detail"]


def test_change_password_invalid_token(client: TestClient, tenant_headers):
    """Test change password fails with invalid token."""
    headers = tenant_headers.copy()
    headers["Authorization"] = "Bearer invalid_token"

    response = client.post(
        "/api/v1/auth/change-password",
        json={
            "old_password": "password123",
            "new_password": "NewPassword123!"
        },
        headers=headers
    )

    assert response.status_code == 403
    assert "Could not validate credentials" in response.json()["detail"]


def test_change_password_clears_reset_token(client: TestClient, db: Session, test_user, user_token_headers):
    """Test that change password clears any existing reset token."""
    # Set reset token first
    test_user.reset_token = "some_reset_token"
    test_user.reset_token_expires = "2024-12-31 23:59:59"
    db.commit()
    
    response = client.post(
        "/api/v1/auth/change-password",
        json={
            "old_password": "password123",
            "new_password": "NewPassword123!"
        },
        headers=user_token_headers
    )
    
    assert response.status_code == 200
    
    # Check that reset fields were cleared
    db.refresh(test_user)
    assert test_user.reset_token is None
    assert test_user.reset_token_expires is None
