import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, Magic<PERSON>ock


def test_health_check_success(client: TestClient):
    """Test successful health check."""
    response = client.get("/health")
    
    assert response.status_code == 200
    data = response.json()
    
    # Check response structure
    assert "status" in data
    assert "uptime" in data
    assert "database_status" in data
    
    # Check values
    assert data["status"] == "ok"
    assert data["database_status"] == "ok"
    assert isinstance(data["uptime"], str)


def test_health_check_database_error(client: TestClient):
    """Test health check with database error."""
    with patch('app.main.SessionLocal') as mock_session_local:
        # Mock database connection failure
        mock_db = MagicMock()
        mock_db.execute.side_effect = Exception("Database connection failed")
        mock_session_local.return_value = mock_db
        
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "ok"
        assert "error: Database connection failed" in data["database_status"]
        assert isinstance(data["uptime"], str)


def test_health_check_database_none(client: TestClient):
    """Test health check when database session creation fails."""
    with patch('app.main.SessionLocal') as mock_session_local:
        # Mock session creation failure
        mock_session_local.side_effect = Exception("Session creation failed")
        
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "ok"
        assert "error: Session creation failed" in data["database_status"]


def test_app_initialization():
    """Test that the FastAPI app is properly initialized."""
    from app.main import app
    
    # Check app configuration
    assert app.title == "Authentication Microservice"
    assert app.version == "1.0.0"
    assert app.docs_url == "/docs"
    assert app.redoc_url == "/redoc"
    
    # Check that description is set
    assert "Secure multi-tenant authentication service" in app.description


def test_app_routes_registered():
    """Test that all expected routes are registered."""
    from app.main import app
    
    # Get all route paths
    route_paths = [route.path for route in app.routes]
    
    # Check that main routes are present
    assert "/health" in route_paths
    assert "/docs" in route_paths
    assert "/redoc" in route_paths
    assert "/openapi.json" in route_paths
    
    # Check that auth routes are prefixed correctly
    auth_routes = [path for path in route_paths if path.startswith("/api/v1/auth")]
    assert len(auth_routes) > 0
    
    # Check that admin routes are prefixed correctly
    admin_routes = [path for path in route_paths if path.startswith("/api/v1/admin")]
    assert len(admin_routes) > 0


def test_middleware_order():
    """Test that middleware is added in correct order."""
    from app.main import app

    # Get middleware stack
    middleware_stack = app.user_middleware

    # Check that we have middleware (exact names may vary due to FastAPI internals)
    assert len(middleware_stack) >= 5  # We added 5 middleware

    # Check that middleware classes are present
    middleware_classes = [middleware.cls for middleware in middleware_stack]

    # Import the actual classes to check
    from app.middleware.security import SecurityHeadersMiddleware, TimingAttackProtectionMiddleware
    from app.middleware.simple_rate_limit import SimpleRateLimitMiddleware
    from app.middleware.api_key import APIKeyMiddleware
    from fastapi.middleware.cors import CORSMiddleware

    assert SecurityHeadersMiddleware in middleware_classes
    assert TimingAttackProtectionMiddleware in middleware_classes
    assert SimpleRateLimitMiddleware in middleware_classes
    assert APIKeyMiddleware in middleware_classes
    assert CORSMiddleware in middleware_classes


def test_custom_openapi_schema():
    """Test that custom OpenAPI schema is properly configured."""
    from app.main import app, custom_openapi
    
    # Get the OpenAPI schema
    schema = custom_openapi()
    
    # Check basic structure
    assert "components" in schema
    assert "securitySchemes" in schema["components"]
    assert "APIKeyHeader" in schema["components"]["securitySchemes"]
    
    # Check API key configuration
    api_key_scheme = schema["components"]["securitySchemes"]["APIKeyHeader"]
    assert api_key_scheme["type"] == "apiKey"
    assert api_key_scheme["in"] == "header"
    assert api_key_scheme["name"] == "X-API-Key"
    
    # Check that security is applied to protected paths
    assert "paths" in schema
    for path, path_item in schema["paths"].items():
        if path not in ["/health", "/docs", "/redoc", "/openapi.json"]:
            # Protected paths should have security requirements
            for method, operation in path_item.items():
                if method.lower() in ["get", "post", "put", "patch", "delete"]:
                    assert "security" in operation
                    assert {"APIKeyHeader": []} in operation["security"]


def test_openapi_caching():
    """Test that OpenAPI schema is cached properly."""
    from app.main import app, custom_openapi
    
    # Clear any existing schema
    app.openapi_schema = None
    
    # First call should generate schema
    schema1 = custom_openapi()
    assert app.openapi_schema is not None
    
    # Second call should return cached schema
    schema2 = custom_openapi()
    assert schema1 is schema2


def test_cors_configuration():
    """Test CORS middleware configuration."""
    from app.main import app
    from fastapi.middleware.cors import CORSMiddleware

    # Find CORS middleware
    cors_middleware = None
    for middleware in app.user_middleware:
        if middleware.cls == CORSMiddleware:
            cors_middleware = middleware
            break

    assert cors_middleware is not None

    # Check CORS configuration
    cors_kwargs = cors_middleware.kwargs
    assert cors_kwargs["allow_credentials"] is True
    assert cors_kwargs["allow_methods"] == ["*"]
    assert cors_kwargs["allow_headers"] == ["*"]


def test_start_time_initialization():
    """Test that start time is properly initialized."""
    from app.main import start_time
    from datetime import datetime
    
    assert isinstance(start_time, datetime)
    
    # Should be recent (within last few minutes)
    now = datetime.now()
    time_diff = now - start_time
    assert time_diff.total_seconds() < 300  # Less than 5 minutes


def test_security_logger_initialization():
    """Test that security logger is properly initialized."""
    from app.main import security_logger
    
    assert security_logger is not None
    assert hasattr(security_logger, 'info')
    assert hasattr(security_logger, 'warning')
    assert hasattr(security_logger, 'error')


def test_admin_routes_not_in_schema():
    """Test that admin routes are excluded from OpenAPI schema."""
    from app.main import app
    
    # Check that admin router is included but not in schema
    admin_router_found = False
    for route in app.routes:
        if hasattr(route, 'path') and route.path.startswith("/api/v1/admin"):
            admin_router_found = True
            break
    
    assert admin_router_found
    
    # Admin routes should be accessible but not documented
    # This is controlled by include_in_schema=False parameter
