import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from uuid import UUID

from app.main import app
from app.db.models import User


def test_register_success(client: TestClient, db: Session, tenant_headers):
    """Test successful user registration with valid data."""
    email = "<EMAIL>"
    password = "StrongPassword123!"
    
    response = client.post(
        "/api/v1/auth/register",
        json={"email": email, "password": password},
        headers=tenant_headers
    )

    assert response.status_code == 200
    data = response.json()
    
    # Check response data
    assert data["email"] == email
    assert "id" in data
    assert "tenant_id" in data
    assert data["is_active"] is True
    
    # Verify user was created in database
    user = db.query(User).filter(User.email == email).first()
    assert user is not None
    assert user.email == email
    assert user.tenant_id == UUID(data["tenant_id"])


def test_register_duplicate_email(client: TestClient, db: Session, tenant_headers, test_user):
    """Test registration fails with already registered email."""
    response = client.post(
        "/api/v1/auth/register",
        json={"email": test_user.email, "password": "AnotherPassword123!"},
        headers=tenant_headers
    )
    
    assert response.status_code == 400
    assert "already exists" in response.json()["detail"]


def test_register_invalid_email(client: TestClient, tenant_headers):
    """Test registration fails with invalid email format."""
    response = client.post(
        "/api/v1/auth/register",
        json={"email": "invalid-email", "password": "Password123!"},
        headers=tenant_headers
    )
    
    assert response.status_code == 422  # Validation error